"""
Основной файл обработчиков для студентских тестов
Объединяет все модули в единый роутер
"""
import logging
from aiogram import Router, F
from aiogram.types import CallbackQuery
from aiogram.fsm.context import FSMContext

# Импортируем все модули
from .base_handlers import router as base_router
from .course_entry_handlers import router as course_entry_router
from .month_handlers import router as month_handlers_router

from ..utils import check_if_id_in_callback_data

# Настройка логгера
logger = logging.getLogger(__name__)

# Создаем основной роутер и подключаем все модули
router = Router()
router.include_router(base_router)
router.include_router(course_entry_router)
router.include_router(month_handlers_router)

# Регистрируем обработчики quiz_system
from common.quiz_system import register_quiz_handlers
from .states import StudentTestsStates

register_quiz_handlers(
    router=router,
    test_state=StudentTestsStates.test_in_progress
)

# Регистрируем обработчики микротем для входных тестов месяца
from common.microtopics.register_handlers import register_month_entry_test_microtopics_handlers, register_month_control_test_microtopics_handlers
from .keyboards import get_back_to_test_kb

register_month_entry_test_microtopics_handlers(
    router=router,
    states_group=StudentTestsStates,
    detailed_callback_prefix="student_month_entry_page",
    summary_callback_prefix="student_month_entry_summary_page",
    detailed_state=StudentTestsStates.month_entry_detailed_stats,
    summary_state=StudentTestsStates.month_entry_summary_stats,
    result_state=StudentTestsStates.month_entry_result,
    back_keyboard_func=get_back_to_test_kb,
    items_per_page_detailed=15,
    items_per_page_summary=15,
    premium_check=False  # Студенты имеют доступ к статистике своих тестов
)

# Регистрируем обработчики микротем для контрольных тестов месяца
register_month_control_test_microtopics_handlers(
    router=router,
    states_group=StudentTestsStates,
    detailed_callback_prefix="student_month_control_page",
    summary_callback_prefix="student_month_control_summary_page",
    detailed_state=StudentTestsStates.month_control_detailed_stats,
    summary_state=StudentTestsStates.month_control_summary_stats,
    result_state=StudentTestsStates.month_control_result,
    back_keyboard_func=get_back_to_test_kb,
    items_per_page_detailed=15,
    items_per_page_summary=15,
    premium_check=False  # Студенты имеют доступ к статистике своих тестов
)

# Регистрируем обработчики пагинации для входного теста курса
from common.microtopics.handlers import handle_microtopics_pagination_universal

@router.callback_query(StudentTestsStates.course_entry_detailed_stats, F.data.startswith("student_course_entry_page_"))
async def handle_course_entry_detailed_pagination(callback: CallbackQuery, state: FSMContext):
    """Обработчик пагинации детальной статистики входного теста курса"""
    await handle_microtopics_pagination_universal(
        callback=callback,
        state=state,
        callback_prefix="student_course_entry_page",
        display_mode="detailed",
        role="student"
    )

@router.callback_query(StudentTestsStates.course_entry_summary_stats, F.data.startswith("student_course_entry_summary_page_"))
async def handle_course_entry_summary_pagination(callback: CallbackQuery, state: FSMContext):
    """Обработчик пагинации сводки входного теста курса"""
    await handle_microtopics_pagination_universal(
        callback=callback,
        state=state,
        callback_prefix="student_course_entry_summary_page",
        display_mode="summary",
        role="student"
    )

# Обработчики возврата из изображений входного теста курса
@router.callback_query(StudentTestsStates.course_entry_detailed_stats, F.data == "back_from_microtopics_image")
async def back_from_course_entry_detailed_image(callback: CallbackQuery, state: FSMContext):
    """Возврат из детальной статистики входного теста курса"""
    await back_to_course_entry_result(callback, state)

@router.callback_query(StudentTestsStates.course_entry_summary_stats, F.data == "back_from_microtopics_image")
async def back_from_course_entry_summary_image(callback: CallbackQuery, state: FSMContext):
    """Возврат из сводки входного теста курса"""
    await back_to_course_entry_result(callback, state)

# Основная функция для обработки главного меню тестов
async def handle_main(callback, state=None, user_role: str = None):
    """Обработчик главного меню тестов"""
    from aiogram.types import CallbackQuery, Message
    from .keyboards import get_tests_menu_by_tariff
    from .states import StudentTestsStates

    text = (
        "🧠 Тест-отчет\n\n"
        "В этом разделе ты можешь пройти входные и контрольные тесты "
        "и посмотреть, как растёт твой уровень знаний.\n\n"
        "Выбери тип теста:"
    )

    if isinstance(callback, CallbackQuery):
        keyboard = await get_tests_menu_by_tariff(callback.from_user.id)
        try:
            await callback.message.edit_text(text, reply_markup=keyboard)
        except Exception:
            await callback.message.answer(text, reply_markup=keyboard)
    elif isinstance(callback, Message):
        keyboard = await get_tests_menu_by_tariff(callback.from_user.id)
        await callback.answer(text, reply_markup=keyboard)

    if state:
        await state.set_state(StudentTestsStates.main)

# Функции для детальной аналитики входного теста курса
async def show_student_course_entry_microtopics_detailed(callback: CallbackQuery, state: FSMContext, test_result_id: int):
    """Показать детальную статистику по микротемам входного теста курса для студента"""
    from common.microtopics.handlers import show_course_entry_test_microtopics_universal
    from .keyboards import get_back_to_test_kb
    from database import CourseEntryTestResultRepository

    try:
        # Получаем результат теста для формирования подписи
        test_result = await CourseEntryTestResultRepository.get_by_id(test_result_id)
        if test_result:
            caption = f"📊 Детальная статистика входного теста курса\n🎯 {test_result.score_percentage}% ({test_result.correct_answers}/{test_result.total_questions})"
        else:
            caption = "📊 Детальная статистика входного теста курса"

        await show_course_entry_test_microtopics_universal(
            callback=callback,
            state=state,
            test_result_id=test_result_id,
            target_state=StudentTestsStates.course_entry_detailed_stats,
            callback_prefix="student_course_entry_page",
            back_keyboard_func=get_back_to_test_kb,
            display_mode="detailed",
            items_per_page=15,
            caption=caption,
            premium_check=False  # Студенты имеют доступ к статистике своих тестов
        )

    except Exception as e:
        logger.error(f"Ошибка при получении детальной статистики: {e}")
        await callback.message.edit_text(
            "❌ Ошибка при получении детальной статистики",
            reply_markup=get_back_to_test_kb()
        )


async def show_student_course_entry_microtopics_summary(callback: CallbackQuery, state: FSMContext, test_result_id: int):
    """Показать сводку по сильным/слабым темам входного теста курса для студента"""
    from common.microtopics.handlers import show_course_entry_test_microtopics_universal
    from .keyboards import get_back_to_test_kb
    from database import CourseEntryTestResultRepository

    try:
        # Получаем результат теста для формирования подписи
        test_result = await CourseEntryTestResultRepository.get_by_id(test_result_id)
        if test_result:
            caption = f"💪 Сводка по входному тесту курса\n🎯 {test_result.score_percentage}% ({test_result.correct_answers}/{test_result.total_questions})"
        else:
            caption = "💪 Сводка по входному тесту курса"

        await show_course_entry_test_microtopics_universal(
            callback=callback,
            state=state,
            test_result_id=test_result_id,
            target_state=StudentTestsStates.course_entry_summary_stats,
            callback_prefix="student_course_entry_summary_page",
            back_keyboard_func=get_back_to_test_kb,
            display_mode="summary",
            items_per_page=15,
            caption=caption,
            premium_check=False  # Студенты имеют доступ к статистике своих тестов
        )

    except Exception as e:
        logger.error(f"Ошибка при получении сводки: {e}")
        await callback.message.edit_text(
            "❌ Ошибка при получении сводки",
            reply_markup=get_back_to_test_kb()
        )


async def back_to_course_entry_result(callback: CallbackQuery, state: FSMContext):
    """Возврат к результату входного теста курса"""
    try:
        # Получаем ID результата теста из состояния
        data = await state.get_data()
        test_result_id = data.get('course_entry_test_result_id')

        if not test_result_id:
            # Если ID не найден, возвращаемся к выбору предметов
            await handle_course_entry_subjects(callback, state)
            return

        # Получаем результат теста и показываем его
        from database import CourseEntryTestResultRepository
        from common.statistics import format_course_entry_test_result
        from aiogram.types import InlineKeyboardButton, InlineKeyboardMarkup

        test_result = await CourseEntryTestResultRepository.get_by_id(test_result_id)
        if not test_result:
            await callback.message.answer(
                "❌ Результат теста не найден",
                reply_markup=get_back_to_test_kb()
            )
            return

        # Форматируем результаты
        result_text = await format_course_entry_test_result(test_result)

        # Создаем кнопки для детальной аналитики
        buttons = [
            [InlineKeyboardButton(
                text="📊 Проценты по микротемам",
                callback_data=f"student_course_entry_detailed_{test_result.id}"
            )],
            [InlineKeyboardButton(
                text="💪 Сильные/слабые темы",
                callback_data=f"student_course_entry_summary_{test_result.id}"
            )]
        ]
        buttons.extend(get_back_to_test_kb().inline_keyboard)

        # Удаляем изображение и отправляем новое сообщение
        await callback.message.delete()
        await callback.message.answer(
            result_text,
            reply_markup=InlineKeyboardMarkup(inline_keyboard=buttons)
        )
        await state.set_state(StudentTestsStates.course_entry_result)

    except Exception as e:
        logger.error(f"Ошибка при возврате к результату входного теста курса: {e}")
        await callback.message.answer(
            "❌ Ошибка при возврате к результату",
            reply_markup=get_back_to_test_kb()
        )



async def handle_test_in_progress(callback, state=None, user_role: str = None):
    """Обработчик состояния прохождения теста"""
    await handle_main(callback, state, user_role)

# Обработчики для входного теста курса
async def handle_course_entry_subjects(callback, state=None, user_role: str = None):
    """Обработчик выбора предметов для входного теста курса"""
    from .keyboards import get_test_subjects_kb
    from aiogram.types import CallbackQuery, Message

    # Обработка разных типов callback
    if isinstance(callback, CallbackQuery):
        # Это CallbackQuery
        await callback.message.edit_text(
            "Выберите предмет для входного теста курса:",
            reply_markup=await get_test_subjects_kb("course_entry", user_id=callback.from_user.id)
        )
        if state:
            await state.set_state(StudentTestsStates.course_entry_subjects)
    elif isinstance(callback, Message):
        # Это Message
        await callback.answer(
            "Выберите предмет для входного теста курса:",
            reply_markup=await get_test_subjects_kb("course_entry", user_id=callback.from_user.id)
        )

async def handle_course_entry_subject_selected(callback, state=None, user_role: str = None):
    """Обработчик после выбора предмета для входного теста курса"""
    await handle_course_entry_subjects(callback, state, user_role)

# Обработчики для входного теста месяца
async def handle_month_entry_subjects(callback, state=None, user_role: str = None):
    """Обработчик выбора предметов для входного теста месяца"""
    from .keyboards import get_test_subjects_kb
    from aiogram.types import CallbackQuery, Message

    # Обработка разных типов callback
    if isinstance(callback, CallbackQuery):
        # Это CallbackQuery
        await callback.message.edit_text(
            "Выберите предмет для входного теста месяца:",
            reply_markup=await get_test_subjects_kb("month_entry", user_id=callback.from_user.id)
        )
        if state:
            await state.set_state(StudentTestsStates.month_entry_subjects)
    elif isinstance(callback, Message):
        # Это Message
        await callback.answer(
            "Выберите предмет для входного теста месяца:",
            reply_markup=await get_test_subjects_kb("month_entry", user_id=callback.from_user.id)
        )

async def handle_month_entry_subject_selected(callback, state=None, user_role: str = None):
    """Обработчик после выбора предмета для входного теста месяца"""
    await handle_month_entry_subjects(callback, state, user_role)



# Обработчики для контрольного теста месяца
async def handle_month_control_subjects(callback, state=None, user_role: str = None):
    """Обработчик выбора предметов для контрольного теста месяца"""
    from .keyboards import get_test_subjects_kb
    from aiogram.types import CallbackQuery, Message

    # Обработка разных типов callback
    if isinstance(callback, CallbackQuery):
        # Это CallbackQuery
        await callback.message.edit_text(
            "Выберите предмет для контрольного теста месяца:",
            reply_markup=await get_test_subjects_kb("month_control", user_id=callback.from_user.id)
        )
        if state:
            await state.set_state(StudentTestsStates.month_control_subjects)
    elif isinstance(callback, Message):
        # Это Message
        await callback.answer(
            "Выберите предмет для контрольного теста месяца:",
            reply_markup=await get_test_subjects_kb("month_control", user_id=callback.from_user.id)
        )

async def handle_month_control_subject_selected(callback, state=None, user_role: str = None):
    """Обработчик после выбора предмета для контрольного теста месяца"""
    await handle_month_control_subjects(callback, state, user_role)



# Обработчики для состояний подтверждения
async def handle_course_entry_confirmation(callback, state=None, user_role: str = None):
    """Обработчик состояния подтверждения входного теста курса для навигации"""
    await handle_course_entry_subjects(callback, state, user_role)

async def handle_month_entry_confirmation(callback, state=None, user_role: str = None):
    """Обработчик состояния подтверждения входного теста месяца для навигации"""
    await handle_month_entry_subjects(callback, state, user_role)

async def handle_month_control_confirmation(callback, state=None, user_role: str = None):
    """Обработчик состояния подтверждения контрольного теста месяца для навигации"""
    await handle_month_control_subjects(callback, state, user_role)

# Обработчики для состояний "выбор месяца" (для возврата из результатов)
async def handle_month_entry_month_selected(callback, state=None, user_role: str = None):
    """Обработчик состояния выбора месяца для входного теста месяца"""
    from common.utils import check_if_id_in_callback_data
    from .keyboards import get_month_test_kb
    from aiogram.types import CallbackQuery, Message

    if isinstance(callback, CallbackQuery) and state:
        # Получаем subject_id из callback_data или из состояния
        subject_id = await check_if_id_in_callback_data(
            callback_starts_with="month_entry_sub_",
            callback=callback,
            state=state,
            id_type="subject_id"
        )

        if subject_id:
            await callback.message.edit_text(
                f"Выберите месяц для входного теста:",
                reply_markup=await get_month_test_kb("month_entry", str(subject_id), user_id=callback.from_user.id)
            )
            await state.set_state(StudentTestsStates.month_entry_subject_selected)
            return

    # Если нет данных о предмете, возвращаемся к выбору предмета
    await handle_month_entry_subjects(callback, state, user_role)

async def handle_month_control_month_selected(callback, state=None, user_role: str = None):
    """Обработчик состояния выбора месяца для контрольного теста месяца"""
    from .keyboards import get_month_test_kb
    from aiogram.types import CallbackQuery, Message

    if isinstance(callback, CallbackQuery) and state:
        # Получаем subject_id из callback_data или из состояния
        subject_id = await check_if_id_in_callback_data(
            callback_starts_with="month_control_sub_",
            callback=callback,
            state=state,
            id_type="subject_id"
        )

        if subject_id:
            await callback.message.edit_text(
                f"Выберите месяц для контрольного теста:",
                reply_markup=await get_month_test_kb("month_control", str(subject_id), user_id=callback.from_user.id)
            )
            await state.set_state(StudentTestsStates.month_control_subject_selected)
            return

    # Если нет данных о предмете, возвращаемся к выбору предмета
    await handle_month_control_subjects(callback, state, user_role)
