from aiogram.fsm.state import StatesGroup, State
from common.tests_statistics.register_handlers import get_transitions_handlers

# Используем конкретные состояния для учителя
class TeacherTestsStatisticsStates(StatesGroup):
    main = State()

    # Состояния для входного теста курса
    course_entry_select_subject = State()  # Выбор предмета (вместо группы)
    course_entry_select_user = State()     # Выбор незарегистрированного пользователя
    course_entry_result = State()          # Результат пользователя
    course_entry_result_display = State()  # Детальное отображение результата
    course_entry_detailed_microtopics = State()  # Детальные микротемы с изображениями
    course_entry_summary_microtopics = State()   # Сводка микротем с изображениями

    # Состояния для входного теста месяца
    month_entry_select_group = State()
    month_entry_select_month = State()
    month_entry_select_student = State()   # Выбор студента
    month_entry_result = State()
    month_entry_result_display = State()   # Детальное отображение результата
    month_entry_detailed_microtopics = State()  # Детальные микротемы с изображениями
    month_entry_summary_microtopics = State()   # Сводка микротем с изображениями

    # Состояния для контрольного теста месяца
    month_control_select_group = State()
    month_control_select_month = State()
    month_control_select_student = State() # Выбор студента
    month_control_result = State()
    month_control_result_display = State() # Детальное отображение результата
    month_control_detailed_microtopics = State()  # Детальные микротемы с изображениями
    month_control_summary_microtopics = State()   # Сводка микротем с изображениями

    # Состояния для пробного ЕНТ
    ent_select_group = State()
    ent_select_student = State()
    ent_result = State()
    ent_result_display = State()           # Детальное отображение результата

# Создаем словари переходов и обработчиков для учителя
STATE_TRANSITIONS, STATE_HANDLERS = get_transitions_handlers(TeacherTestsStatisticsStates, "teacher")
