#!/usr/bin/env python3
"""
Скрипт для тестирования отстающих студентов
Изменяет даты выполнения ДЗ на старые для тестирования
"""

import asyncio
from datetime import datetime, timedelta
from database import HomeworkResultRepository, StudentRepository, GroupRepository

async def make_students_lagging():
    """Сделать некоторых студентов отстающими для тестирования"""
    try:
        # Получаем все группы
        groups = await GroupRepository.get_all()
        
        if not groups:
            print("❌ Группы не найдены")
            return
            
        # Берем первую группу для тестирования
        test_group = groups[0]
        print(f"🎯 Тестируем группу: {test_group.name}")
        
        # Получаем студентов группы
        students = await StudentRepository.get_by_group(test_group.id)
        
        if not students:
            print("❌ Студенты в группе не найдены")
            return
            
        print(f"👥 Найдено студентов: {len(students)}")
        
        # Берем первого студента и делаем его отстающим
        if len(students) > 0:
            student = students[0]
            print(f"🎯 Делаем отстающим: {student.user.name}")
            
            # Получаем результаты ДЗ студента
            homework_results = await HomeworkResultRepository.get_by_student(student.id)
            
            if homework_results:
                # Изменяем дату последнего результата на 7 дней назад
                old_date = datetime.now() - timedelta(days=7)
                
                for result in homework_results[-3:]:  # Последние 3 результата
                    await HomeworkResultRepository.update(
                        result.id,
                        created_at=old_date
                    )
                    print(f"   📅 Изменена дата результата {result.id} на {old_date}")
                    
                print(f"✅ Студент {student.user.name} теперь отстающий (7 дней без ДЗ)")
            else:
                print(f"   ❌ У студента {student.user.name} нет результатов ДЗ")
        
        # Если есть второй студент, оставляем его активным для сравнения
        if len(students) > 1:
            student2 = students[1]
            print(f"✅ Студент {student2.user.name} остается активным для сравнения")
            
        print(f"\n🧪 Тестирование готово! Группа: {test_group.name} (ID: {test_group.id})")
        print("Теперь можете проверить статистику группы в боте")
        
    except Exception as e:
        print(f"❌ Ошибка: {e}")
        import traceback
        traceback.print_exc()

async def restore_students():
    """Восстановить нормальные даты"""
    try:
        # Получаем все результаты ДЗ за последние 10 дней
        from database.repositories import HomeworkResultRepository
        
        # Устанавливаем текущую дату для всех результатов
        current_date = datetime.now()
        
        # Здесь можно добавить логику восстановления, если нужно
        print("🔄 Для восстановления дат используйте базу данных напрямую")
        
    except Exception as e:
        print(f"❌ Ошибка при восстановлении: {e}")

if __name__ == "__main__":
    print("🧪 Скрипт тестирования отстающих студентов")
    print("1. make - сделать студентов отстающими")
    print("2. restore - восстановить даты")
    
    choice = input("Выберите действие (make/restore): ").strip().lower()
    
    if choice == "make":
        asyncio.run(make_students_lagging())
    elif choice == "restore":
        asyncio.run(restore_students())
    else:
        print("❌ Неверный выбор")
